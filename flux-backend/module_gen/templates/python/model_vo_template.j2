# -*- coding:utf-8 -*-
from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from typing import List, Literal, Optional, Union
from module_admin.annotation.pydantic_annotation import as_query


class {{ tableName|snake_to_pascal_case }}Model(BaseModel):
    """
    表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)
    {% for column in columns %}
    {{ column.columnName }}: Optional[{{ column.pythonType }}] =  Field(default=None, description='{{ column.columnComment }}')
    {% if column.queryType == 'BETWEEN' %}
    begin_{{ column.columnName }}: Optional[{{ column.pythonType }}] =  Field(default=None, description='{{ column.columnComment }}最小值')
    {% endif %}
    {% if column.queryType == 'BETWEEN' %}
    end_{{ column.columnName }}: Optional[{{ column.pythonType }}] =  Field(default=None, description='{{ column.columnComment }}最大值')
    {% endif %}
    {% endfor %}


@as_query
class {{ tableName|snake_to_pascal_case }}PageModel({{ tableName|snake_to_pascal_case }}Model):
    """
    分页查询模型
    """
    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')
